from concurrent.futures import Thread<PERSON>oolExecutor
from django.shortcuts import render, redirect
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.urls import reverse
from django.utils import timezone
from django.core.cache import cache
from myproject.utils import *
from .models import VideoComment
from .workorder_data import *
from .aerotask_chart.aerotask_chart import get_chart_data, get_chart_and_performance_data
from .telemetry_chart.telemetry_data import get_telemetry_data, check_available_sets_with_telemetry
from .telemetry_chart.telemetry_chart import generate_telemetry_chart
from .weather_history_service import WeatherHistoryService, WeatherHistoryServiceError
from .weather_history_service import WeatherHistoryService, WeatherHistoryServiceError
from .gps_chart.gps_chart import generate_gps_speed_chart
from .gps_chart.gps_data import process_navirec_timeline_data
import logging
import pytz
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import time
import json
import pytz
import requests

logger = logging.getLogger(__name__)

def get_common_context(
    project_name: Optional[str],
    service_code: Optional[str],
    set_name: Optional[str],
    customer: Optional[str],
    activity_date: Optional[str],
    workorder_name: Optional[str],
    limit: int = 30
) -> Dict[str, Any]:
    workorders, dropdown_values = get_workorders_and_dropdown_values(
        project_name=project_name,
        service_code=service_code,
        set_name=set_name,
        customer=customer,
        activity_date=activity_date,
        workorder_name=workorder_name,
        limit=limit
    )

    return {
        'workorders': workorders,
        'dropdown_values': dropdown_values,
        'chart': None,
        'metadata': None,
        'selected_project_name': project_name or '',
        'selected_service_code': service_code or '',
        'selected_set_name': set_name or '',
        'selected_customer': customer or '',
        'selected_activity_date': activity_date or '',
        'selected_workorder_name': workorder_name or '',
    }

def workorder_view(request: HttpRequest) -> HttpResponse:
    func_name = "workorder_view"
    params = extract_request_params(request)

    # Format the activity_date if it exists
    activity_date = params['activity_date']

    # Check if we should directly show the first workorder
    # This happens when coming from a day cube click in setworkload
    direct_to_first = request.GET.get('direct_to_first') == 'true'
    # We could also check the referrer, but it's not necessary
    # coming_from_setworkload = 'setworkload' in request.META.get('HTTP_REFERER', '')

    # For multiple selections, check if set_name is a list with values
    has_set_filter = False
    if isinstance(params['set_name'], list) and params['set_name']:
        has_set_filter = True
    elif isinstance(params['set_name'], str) and params['set_name']:
        has_set_filter = True

    # If we have filters and should direct to first workorder
    if direct_to_first and has_set_filter:

        try:
            # Get the first workorder that matches the filters
            # We only need one result, so set limit=1
            workorders = get_workorders(
                project_name=params['project_name'],
                service_code=params['service_code'],
                set_name=params['set_name'],
                customer=params['customer'],
                activity_date=activity_date,
                workorder_name=params['workorder_name'],
                limit=1
            )

            # If we found a workorder, redirect to its detail view
            if workorders and len(workorders) > 0:
                first_workorder = workorders[0]
                workorder_id = first_workorder.workorder_id

                # Construct the redirect URL with all the original parameters
                redirect_url = reverse('workorder_detail', kwargs={'workorder_id': workorder_id})

                # Import urllib.parse for URL encoding
                import urllib.parse

                # Build the redirect URL with all parameters
                redirect_url = f"{redirect_url}?from_direct_to_first=true"

                # Add project_name parameters (could be multiple)
                if isinstance(params['project_name'], list):
                    for p_name in params['project_name']:
                        if p_name:
                            redirect_url += f"&project_name={urllib.parse.quote(p_name)}"
                elif params['project_name']:
                    redirect_url += f"&project_name={urllib.parse.quote(params['project_name'])}"

                # Add service_code parameters (could be multiple)
                if isinstance(params['service_code'], list):
                    for s_code in params['service_code']:
                        if s_code:
                            redirect_url += f"&service_code={urllib.parse.quote(s_code)}"
                elif params['service_code']:
                    redirect_url += f"&service_code={urllib.parse.quote(params['service_code'])}"

                # Add set_name parameters (could be multiple)
                if isinstance(params['set_name'], list):
                    for s_name in params['set_name']:
                        if s_name:
                            redirect_url += f"&set_name={urllib.parse.quote(s_name)}"
                elif params['set_name']:
                    redirect_url += f"&set_name={urllib.parse.quote(params['set_name'])}"

                # Add remaining parameters
                if activity_date:
                    redirect_url += f"&activity_date={urllib.parse.quote(activity_date)}"
                if params['workorder_name']:
                    redirect_url += f"&workorder_name={urllib.parse.quote(params['workorder_name'])}"

                return redirect(redirect_url)
            else:
                # No workorders found matching the filters
                pass
        except Exception:
            # Error redirecting to first workorder
            pass

    # If we didn't redirect, render the normal view
    context = get_common_context(
        project_name=params['project_name'],
        service_code=params['service_code'],
        set_name=params['set_name'],
        customer=params['customer'],
        activity_date=activity_date,
        workorder_name=params['workorder_name']
    )
    return render(request, 'workorders/workorder_view.html', context)

def workorder_detail(request: HttpRequest, workorder_id: int) -> HttpResponse:
    func_name = "workorder_detail"
    params = extract_request_params(request)

    with ThreadPoolExecutor(max_workers=6) as executor:
        workorder_future = executor.submit(get_workorders_and_dropdown_values, workorder_id=workorder_id)
        common_context_future = executor.submit(get_common_context, **params)
        chart_future = executor.submit(get_chart_and_performance_data, workorder_id)
        metadata_future = executor.submit(get_workorder_metadata, workorder_id)
        activities_future = executor.submit(get_activity_data, workorder_id)
        daily_reports_future = executor.submit(get_daily_reports, workorder_id)

    workorder, _ = workorder_future.result()

    if workorder is None:
        logger.warning(f"[{func_name}] Workorder not found: {workorder_id}")
        return render(request, 'workorders/error.html', {'error': 'Workorder not found'})

    common_context = common_context_future.result()
    chart_html, performance_data, performance_summary = chart_future.result()
    metadata = metadata_future.result()
    activities = activities_future.result()
    daily_reports = daily_reports_future.result()

    if metadata:
        metadata['Completed Date'] = metadata['Completed Date'].strftime('%b. %d, %Y, %I:%M %p') if metadata['Completed Date'] else 'N/A'
        is_completed, completion_date = metadata['Marked as Completed']
        metadata['Marked as Completed'] = f"{'True' if is_completed else 'False'} ({completion_date.strftime('%b. %d, %Y, %I:%M %p') if completion_date else 'N/A'})"

        # Add Daily Reports to metadata if available
        if daily_reports:
            # First, process all reports to extract dates and create report data
            processed_reports = []
            for report in daily_reports:
                # Use the actual report date from the dates column instead of created_at
                formatted_date = None
                date_range = ""

                # Robust date processing - handle different data formats
                dates = None

                # First, normalize the dates field to a list
                if report['dates'] is not None:
                    if isinstance(report['dates'], list):
                        # Already a list - use directly if not empty
                        dates = report['dates'] if len(report['dates']) > 0 else None
                    elif isinstance(report['dates'], str) and report['dates'].strip():
                        # String format - try to parse as JSON
                        try:
                            import json
                            parsed_dates = json.loads(report['dates'])
                            if isinstance(parsed_dates, list) and len(parsed_dates) > 0:
                                dates = parsed_dates
                        except (json.JSONDecodeError, TypeError):
                            # If JSON parsing fails, treat as single date string
                            dates = [report['dates'].strip()]
                    # For any other data type, skip processing (dates will remain None)

                # Process the dates if we have valid data
                if dates and len(dates) > 0:
                    # Use the first date as the primary display date
                    first_date_raw = dates[0]

                    # Ensure we have a valid first date
                    if first_date_raw is not None:
                        # Handle different date formats
                        if isinstance(first_date_raw, str) and 'T' in first_date_raw:
                            primary_date = first_date_raw.split('T')[0]  # Get just the date part
                        else:
                            primary_date = str(first_date_raw)  # Convert to string

                        # Validate that we have a non-empty date string
                        if primary_date and primary_date.strip():
                            formatted_date = primary_date.strip()

                            # Format as a date range if multiple dates and they're different
                            if len(dates) > 1:
                                last_date_raw = dates[-1]
                                if last_date_raw is not None:
                                    if isinstance(last_date_raw, str) and 'T' in last_date_raw:
                                        end_date = last_date_raw.split('T')[0]
                                    else:
                                        end_date = str(last_date_raw)

                                    # Only show range if dates are actually different and end_date is valid
                                    if end_date and end_date.strip() and end_date.strip() != primary_date:
                                        date_range = f" (to {end_date.strip()})"

                # If we still don't have a formatted_date, use fallback
                if not formatted_date:
                    formatted_date = "null"

                # Store the processed report data
                processed_reports.append({
                    'formatted_date': formatted_date,
                    'date_range': date_range,
                    'report_url': report['report_url'],
                    'sort_date': formatted_date if formatted_date != "null" else "0000-01-01"  # Put nulls at the beginning when sorting newest first
                })

            # Sort reports by date (newest to oldest)
            processed_reports.sort(key=lambda x: x['sort_date'], reverse=True)

            # Create the HTML links from sorted reports
            report_links = []
            for processed_report in processed_reports:
                report_links.append(f'<div class="mb-1"><a href="{processed_report["report_url"]}" target="_blank" class="text-blue-600 hover:text-blue-800">{processed_report["formatted_date"]}{processed_report["date_range"]}</a></div>')

            if report_links:
                metadata['Daily Reports'] = '<div class="flex flex-col">' + ''.join(report_links) + '</div>'
            else:
                # Get workorder date range
                workorder_start_date, workorder_end_date = get_workorder_date_range(workorder_id)
                date_range_str = ""
                if workorder_start_date:
                    if workorder_end_date and workorder_end_date != workorder_start_date:
                        date_range_str = f"({workorder_start_date.strftime('%Y-%m-%d')} to {workorder_end_date.strftime('%Y-%m-%d')})"
                    else:
                        date_range_str = f"({workorder_start_date.strftime('%Y-%m-%d')})"

                metadata['Daily Reports'] = f'No Daily Reports available in the workorder\'s date range {date_range_str}'
        else:
            # Get workorder date range
            workorder_start_date, workorder_end_date = get_workorder_date_range(workorder_id)
            date_range_str = ""
            if workorder_start_date:
                if workorder_end_date and workorder_end_date != workorder_start_date:
                    date_range_str = f"({workorder_start_date.strftime('%Y-%m-%d')} to {workorder_end_date.strftime('%Y-%m-%d')})"
                else:
                    date_range_str = f"({workorder_start_date.strftime('%Y-%m-%d')})"

            metadata['Daily Reports'] = f'No Daily Reports available in the workorder\'s date range {date_range_str}'

    # Get telemetry data
    telemetry_chart = None
    telemetry_start_date = None
    telemetry_end_date = None
    available_sets = []
    preset_details_data = {}

    # Get set_code and timezone for the workorder
    set_code, timezone_name = get_set_code_and_timezone(workorder_id)

    # Get workorder date range - always use the workorder's start and end dates
    workorder_start_date, workorder_end_date = get_workorder_date_range(workorder_id)

    # Set telemetry date range to workorder date range
    if workorder_start_date:
        telemetry_start_date = workorder_start_date.replace(hour=0, minute=0, second=0, microsecond=0)
    else:
        # Default to today if no workorder date is available
        telemetry_start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

    if workorder_end_date:
        telemetry_end_date = workorder_end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
    else:
        # Default to start date + 1 day if no workorder end date is available
        telemetry_end_date = telemetry_start_date.replace(hour=23, minute=59, second=59, microsecond=999999) + timedelta(days=1)

    # Make sure end date is not before start date
    if telemetry_end_date < telemetry_start_date:
        telemetry_start_date, telemetry_end_date = telemetry_end_date, telemetry_start_date

    # Check if there are sets with telemetry data for the workorder's date range
    if set_code:
        available_sets = check_available_sets_with_telemetry(telemetry_start_date, telemetry_end_date)
        
        # Only get telemetry data if the set is available
        if set_code in available_sets:
            # Get telemetry data and generate chart
            df, category_orders, applying_df, preset_details_data, initializing_df = get_telemetry_data(
                set_code, timezone_name, telemetry_start_date, telemetry_end_date
            )
            
            if not df.empty:
                date_range_str = f"{telemetry_start_date.strftime('%Y-%m-%d')} to {telemetry_end_date.strftime('%Y-%m-%d')}"
                display_name = metadata.get('Set') if metadata else set_code
                telemetry_chart, preset_details_data = generate_telemetry_chart(
                    df=df,
                    category_orders=category_orders,
                    title=f"Telemetry Data for {display_name} ({date_range_str})",
                    applying_df=applying_df,
                    initializing_df=initializing_df,
                    preset_details_data=preset_details_data,
                    workorder_id=workorder_id
                )

    # GPS chart is now handled dynamically via the GPS tab and gps_chart_api endpoint
    # No hardcoded GPS data needed here

    context = {
        **common_context,
        'workorder': workorder,
        'aerotask_chart': chart_html,
        'performance_data': performance_data,
        'performance_summary': performance_summary,
        'metadata': metadata,
        'activities': activities,
        'daily_reports': daily_reports,
        'telemetry_chart': telemetry_chart,
        'telemetry_start_date': telemetry_start_date,
        'telemetry_end_date': telemetry_end_date,
        'available_sets': available_sets,
        'full_set_name': set_code,
        'preset_details_data': preset_details_data,
        'workorder_id': workorder_id,
        'workorder_start_date': workorder_start_date,
        'workorder_end_date': workorder_end_date,
        'workorder_timezone': timezone_name,
    }

    response = render(request, 'workorders/workorder_view.html', context)

    # Memory cleanup: Delete large variables AFTER template rendering
    if 'activities' in locals() and activities is not None:
        del activities
    if 'preset_details_data' in locals() and preset_details_data:
        del preset_details_data

    return response

def filter_workorders(request: HttpRequest) -> HttpResponse:
    params = extract_request_params(request)
    context = get_common_context(**params)
    return render(request, 'workorders/workorder_view.html', context)

def reset_filters(request: HttpRequest) -> HttpResponse:
    # Redirect to the base workorder view without any query parameters
    # This ensures all filters are cleared
    return redirect('workorder_view')


@csrf_exempt
def workorder_data(request):
    func_name = "workorder_data"
    try:
        draw = int(request.POST.get('draw', 1))
        start = int(request.POST.get('start', 0))
        length = int(request.POST.get('length', 10))

        # Handle multiple selections
        project_name = request.POST.getlist('project_name[]', None) or request.POST.get('project_name', '')
        service_code = request.POST.getlist('service_code[]', None) or request.POST.get('service_code', '')
        set_name = request.POST.getlist('set_name[]', None) or request.POST.get('set_name', '')
        customer = request.POST.getlist('customer[]', None) or request.POST.get('customer', '')

        workorder_name = request.POST.get('workorder_name', '')
        activity_date = request.POST.get('activity_date', '')

        # Handle client-side pagination request (length = -1 means load all)
        if length == -1:
            # For client-side pagination, load ALL data without any limit
            # Pass None as length to indicate no limit
            data, total_records = get_workorders_flexible(
                project_name=project_name,
                service_code=service_code,
                set_name=set_name,
                customer=customer,
                activity_date=activity_date,
                workorder_name=workorder_name,
                limit=None,  # No limit - get ALL workorders
                offset=0,
                return_count=True
            )
        else:
            # For server-side pagination (fallback)
            data, total_records = get_workorder_data(start, length, project_name, service_code, set_name, customer, workorder_name, activity_date)

        for item in data:
            item['detail_url'] = reverse('workorder_detail', kwargs={'workorder_id': item['workorder_id']})
            # Keep workorder_id for summary functionality
            # del item['workorder_id']

        response = {
            'draw': draw,
            'recordsTotal': total_records,
            'recordsFiltered': total_records,
            'data': data
        }

        return JsonResponse(response)
    except Exception as e:
        logger.error(f"[{func_name}] Error in workorder_data: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
def workorder_summary_data(request):
    """API endpoint to get summary data for a list of workorder IDs."""
    func_name = "workorder_summary_data"
    try:
        if request.method != 'POST':
            return JsonResponse({'error': 'Method not allowed'}, status=405)
        
        # Get workorder IDs from request
        workorder_ids = request.POST.getlist('workorder_ids[]')
        if not workorder_ids:
            return JsonResponse({'data': [], 'averages': {}})
        
        # Convert to integers
        try:
            workorder_ids = [int(wid) for wid in workorder_ids if wid]
        except ValueError:
            return JsonResponse({'error': 'Invalid workorder IDs'}, status=400)
        
        # Get summary data
        summary_data = get_workorders_summary_data(workorder_ids)
        
        # Calculate averages (work with minutes, let frontend format them) - NO ROUNDING for raw precision
        # Exclude workorders from averages only if ALL their values are zero
        if summary_data:
            # Filter out workorders where ALL values are zero
            valid_workorders = []
            for item in summary_data:
                if not (
                    (item['direct_minutes'] or 0) == 0 and
                    (item['delay_minutes'] or 0) == 0 and
                    (item['blade_a_minutes'] or 0) == 0 and
                    (item['blade_b_minutes'] or 0) == 0 and
                    (item['blade_c_minutes'] or 0) == 0 and
                    (item['common_minutes'] or 0) == 0
                ):
                    valid_workorders.append(item)
            
            # Calculate averages including zero values from valid workorders
            direct_values = [item['direct_minutes'] or 0 for item in valid_workorders]
            delay_values = [item['delay_minutes'] or 0 for item in valid_workorders]
            common_values = [item['common_minutes'] or 0 for item in valid_workorders]
            
            # For total blade minutes, calculate per workorder first from valid workorders
            blade_totals = []
            for item in valid_workorders:
                blade_total = (item['blade_a_minutes'] or 0) + (item['blade_b_minutes'] or 0) + (item['blade_c_minutes'] or 0)
                blade_totals.append(blade_total)
            
            averages = {
                'direct_minutes': sum(direct_values) / len(direct_values) if direct_values else 0,
                'delay_minutes': sum(delay_values) / len(delay_values) if delay_values else 0,
                'total_blade_minutes': sum(blade_totals) / len(blade_totals) if blade_totals else 0,
                'common_minutes': sum(common_values) / len(common_values) if common_values else 0,
            }
            
            # Calculate medians including all zero values (no filtering)
            def calculate_median(values):
                if not values:
                    return 0
                sorted_values = sorted(values)
                n = len(sorted_values)
                if n % 2 == 0:
                    return (sorted_values[n//2 - 1] + sorted_values[n//2]) / 2
                else:
                    return sorted_values[n//2]
            
            all_direct_values = [item['direct_minutes'] or 0 for item in summary_data]
            all_delay_values = [item['delay_minutes'] or 0 for item in summary_data]
            all_common_values = [item['common_minutes'] or 0 for item in summary_data]
            all_blade_totals = []
            for item in summary_data:
                blade_total = (item['blade_a_minutes'] or 0) + (item['blade_b_minutes'] or 0) + (item['blade_c_minutes'] or 0)
                all_blade_totals.append(blade_total)
            
            medians = {
                'direct_minutes': calculate_median(all_direct_values),
                'delay_minutes': calculate_median(all_delay_values),
                'total_blade_minutes': calculate_median(all_blade_totals),
                'common_minutes': calculate_median(all_common_values),
            }
        else:
            averages = {
                'direct_minutes': 0,
                'delay_minutes': 0,
                'total_blade_minutes': 0,
                'common_minutes': 0,
            }
            medians = {
                'direct_minutes': 0,
                'delay_minutes': 0,
                'total_blade_minutes': 0,
                'common_minutes': 0,
            }
        
        return JsonResponse({
            'data': summary_data,
            'averages': averages,
            'medians': medians
        })
        
    except Exception as e:
        logger.error(f"[{func_name}] Error in workorder_summary_data: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def get_video_comments(request, workorder_id):
    """API endpoint to get comments for a specific video activity."""
    try:
        blade = request.GET.get('blade')
        activity_type = request.GET.get('activity_type')
        start_datetime_str = request.GET.get('start_datetime')

        if not blade or not activity_type or not start_datetime_str:
            return JsonResponse({
                'error': 'Missing required parameters: blade, activity_type, and start_datetime'
            }, status=400)

        try:
            if start_datetime_str.endswith('Z'):
                # Handle UTC timezone indicator 'Z'
                naive_dt = datetime.fromisoformat(start_datetime_str[:-1])
                start_datetime = timezone.make_aware(naive_dt, pytz.UTC)
            else:
                start_datetime = datetime.fromisoformat(start_datetime_str.replace('Z', '+00:00'))
            
            # Ensure datetime is timezone-aware
            if timezone.is_naive(start_datetime):
                start_datetime = timezone.make_aware(start_datetime, pytz.UTC)
        except ValueError:
            return JsonResponse({
                'error': 'Invalid datetime format for start_datetime'
            }, status=400)

        comments = VideoComment.objects.filter(
            workorder_id=workorder_id,
            blade=blade,
            activity_type=activity_type,
            start_datetime=start_datetime
        ).order_by('-created_at')

        comments_data = []
        for comment in comments:
            comments_data.append({
                'id': comment.id,
                'user_email': comment.user_email,
                'comment_text': comment.comment_text,
                'created_at': comment.created_at.isoformat(),
                'created_at_formatted': comment.created_at.strftime('%Y-%m-%d %H:%M (UTC)')
            })

        return JsonResponse({
            'success': True,
            'comments': comments_data,
            'current_user_email': request.user.email
        })

    except Exception as e:
        logger.error(f"Error fetching video comments: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def add_video_comment(request, workorder_id):
    """API endpoint to add a new video comment."""
    try:
        data = json.loads(request.body)

        blade = data.get('blade')
        activity_type = data.get('activity_type')
        start_datetime_str = data.get('start_datetime')
        comment_text = data.get('comment_text', '').strip()

        if not blade or not activity_type or not start_datetime_str or not comment_text:
            return JsonResponse({
                'error': 'Missing required fields: blade, activity_type, start_datetime, and comment_text'
            }, status=400)

        try:
            if start_datetime_str.endswith('Z'):
                # Handle UTC timezone indicator 'Z'
                naive_dt = datetime.fromisoformat(start_datetime_str[:-1])
                start_datetime = timezone.make_aware(naive_dt, pytz.UTC)
            else:
                start_datetime = datetime.fromisoformat(start_datetime_str.replace('Z', '+00:00'))
            
            # Ensure datetime is timezone-aware
            if timezone.is_naive(start_datetime):
                start_datetime = timezone.make_aware(start_datetime, pytz.UTC)
        except ValueError:
            return JsonResponse({
                'error': 'Invalid datetime format for start_datetime'
            }, status=400)

        # Create the comment
        comment = VideoComment.objects.create(
            user_email=request.user.email,
            workorder_id=workorder_id,
            blade=blade,
            activity_type=activity_type,
            start_datetime=start_datetime,
            comment_text=comment_text
        )

        return JsonResponse({
            'success': True,
            'comment': {
                'id': comment.id,
                'user_email': comment.user_email,
                'comment_text': comment.comment_text,
                'created_at': comment.created_at.isoformat(),
                'created_at_formatted': comment.created_at.strftime('%Y-%m-%d %H:%M (UTC)')
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        logger.error(f"Error adding video comment: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["DELETE"])
def delete_video_comment(request, workorder_id, comment_id):
    """API endpoint to delete a video comment."""
    try:
        # Get the comment
        try:
            comment = VideoComment.objects.get(id=comment_id)
        except VideoComment.DoesNotExist:
            return JsonResponse({
                'error': 'Comment not found'
            }, status=404)

        # Check if the user owns the comment
        if comment.user_email != request.user.email:
            return JsonResponse({
                'error': 'You can only delete your own comments'
            }, status=403)

        # Check if the comment belongs to the specified workorder
        if comment.workorder_id != workorder_id:
            return JsonResponse({
                'error': 'Comment does not belong to this workorder'
            }, status=400)

        # Delete the comment
        comment.delete()

        return JsonResponse({
            'success': True,
            'message': 'Comment deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting video comment: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@require_http_methods(["GET"])
def navirec_api_call(request: HttpRequest) -> JsonResponse:
    """
    Proxy view to call Navirec API with pagination support to retrieve all data.
    Supports different endpoints via 'endpoint' query parameter.
    Supports timezone specification via 'timezone' query parameter.
    Designed for manual testing and exploration via the callNavirecAPI() JavaScript function.
    """
    try:
        # Get Navirec API credentials using the same pattern as other connections
        from myproject.utils import get_navirec_credentials
        account_id, api_key = get_navirec_credentials()

        if not account_id or not api_key:
            logger.error("Navirec API credentials not available")
            return JsonResponse({
                'error': 'Navirec API credentials not configured'
            }, status=500)

        # Get timezone from query parameter (defaults to UTC)
        timezone_param = request.GET.get('timezone', 'UTC')
        
        # Prepare headers for Navirec API
        headers = {
            'Accept': 'application/json; version=1.42.0',
            'Authorization': f'Token {api_key}',
            'Host': 'api.navirec.com',
            'User-Agent': 'WorkorderDataApp/1.0.0'
        }
        
        # Add timezone header if specified and not UTC
        if timezone_param and timezone_param.upper() != 'UTC':
            headers['Accept-Timezone'] = timezone_param
            logger.info(f"Using timezone: {timezone_param}")

        # Get endpoint from query parameter (default to empty for base URL)
        endpoint = request.GET.get('endpoint', '')
        
        # Build the API URL
        base_url = 'https://api.navirec.com'
        if endpoint:
            # If endpoint is specified, format it properly
            if not endpoint.startswith('/'):
                endpoint = '/' + endpoint
            if not endpoint.endswith('/'):
                endpoint = endpoint + '/'
            url = f"{base_url}{endpoint}"
        else:
            # Default to base URL
            url = f"{base_url}/"
        
        # Add ordering parameter for consistent pagination (works for most endpoints)
        params = {'ordering': 'id'}
        
        # Add any additional query parameters passed from the frontend
        # Exclude 'endpoint' and 'timezone' as they're used for URL construction and headers
        for key, value in request.GET.items():
            if key not in ['endpoint', 'timezone'] and value:
                params[key] = value
        
        all_data = []
        page_count = 0
        total_requests = 0
        response_timezone = None
        
        while url:
            total_requests += 1
            page_count += 1

            # Add timeout and make request with retry logic
            max_retries = 3
            response = None

            for attempt in range(max_retries):
                try:
                    response = requests.get(url, headers=headers, params=params, timeout=30)

                    if response.status_code == 200:
                        break
                    elif response.status_code == 429:
                        # Rate limited - wait and retry
                        if attempt < max_retries - 1:
                            time.sleep(2 ** attempt)  # Exponential backoff
                            continue
                        else:
                            return JsonResponse({
                                'error': f'Navirec API rate limit exceeded on page {page_count}. Please try again later.',
                                'pages_collected': page_count - 1,
                                'items_collected': len(all_data)
                            }, status=429)
                    elif response.status_code == 400:
                        # Parse 400 error for better user messages
                        try:
                            error_details = response.json()
                            error_message = error_details.get('message', '') or error_details.get('error', '') or response.text
                        except:
                            error_message = response.text

                        error_lower = error_message.lower()
                        if any(phrase in error_lower for phrase in ['no data', 'no records', 'empty result']):
                            user_error = f'No data available for the requested parameters'
                        elif any(phrase in error_lower for phrase in ['invalid', 'not found', 'does not exist']):
                            user_error = f'Invalid request parameters. Please check your input values.'
                        else:
                            user_error = f'Request failed due to invalid parameters'

                        return JsonResponse({
                            'error': user_error,
                            'pages_collected': page_count - 1,
                            'items_collected': len(all_data)
                        }, status=400)
                    elif response.status_code == 404:
                        return JsonResponse({
                            'error': 'The requested resource was not found',
                            'pages_collected': page_count - 1,
                            'items_collected': len(all_data)
                        }, status=404)
                    else:
                        return JsonResponse({
                            'error': f'API request failed (Error {response.status_code}). Please try again later.',
                            'pages_collected': page_count - 1,
                            'items_collected': len(all_data)
                        }, status=response.status_code)

                except requests.exceptions.Timeout:
                    if attempt == max_retries - 1:
                        return JsonResponse({
                            'error': f'Request timeout on page {page_count}',
                            'pages_collected': page_count - 1,
                            'items_collected': len(all_data)
                        }, status=500)
                    time.sleep(1)
                except requests.exceptions.RequestException as e:
                    if attempt == max_retries - 1:
                        return JsonResponse({
                            'error': f'Request failed on page {page_count}: {str(e)}',
                            'pages_collected': page_count - 1,
                            'items_collected': len(all_data)
                        }, status=500)
                    time.sleep(1)

            if response is None or response.status_code != 200:
                return JsonResponse({
                    'error': f'Failed to get valid response after {max_retries} attempts on page {page_count}',
                    'pages_collected': page_count - 1,
                    'items_collected': len(all_data)
                }, status=500)

            # Capture timezone from response headers on first successful response
            if page_count == 1:
                response_timezone = response.headers.get('Content-Timezone')

            try:
                page_data = response.json()
                
                # Handle different response formats
                if isinstance(page_data, list):
                    # Standard paginated list response
                    all_data.extend(page_data)
                elif isinstance(page_data, dict):
                    # Object response (like base URL) - treat as single item
                    all_data.append(page_data)
                    # For dict responses, don't expect pagination
                    url = None  # Stop pagination loop
                else:
                    return JsonResponse({
                        'error': f'Unexpected response format on page {page_count}: expected list or dict, got {type(page_data).__name__}',
                        'pages_collected': page_count - 1,
                        'items_collected': len(all_data)
                    }, status=500)
                
                # Parse Link header to get next page URL (only for list responses)
                if isinstance(page_data, list):
                    link_header = response.headers.get('Link', '')
                    next_url = None
                    
                    if link_header:
                        # Parse Link header format: <url>; rel="next"
                        links = link_header.split(',')
                        for link in links:
                            if 'rel="next"' in link:
                                # Extract URL from <url>
                                url_part = link.split(';')[0].strip()
                                if url_part.startswith('<') and url_part.endswith('>'):
                                    next_url = url_part[1:-1]  # Remove < and >
                                break
                    
                    # Set up for next iteration
                    url = next_url
                    params = None  # Next URL already includes all parameters
                
                # Safety check to prevent infinite loops
                if total_requests > 100:  # Reasonable limit
                    return JsonResponse({
                        'error': 'Too many pages - stopped at 100 requests for safety',
                        'pages_collected': page_count,
                        'items_collected': len(all_data),
                        'data': all_data
                    }, status=200)  # Still return data collected so far
                
            except ValueError:
                return JsonResponse({
                    'error': f'Invalid JSON response from API on page {page_count}',
                    'details': response.text,
                    'pages_collected': page_count - 1,
                    'items_collected': len(all_data)
                }, status=500)

        # Create comprehensive response with all collected data
        response_data = {
            'data': all_data,
            'pagination_info': {
                'total_pages': page_count,
                'total_items': len(all_data),
                'total_requests': total_requests,
                'response_type': 'list' if len(all_data) > 1 or (len(all_data) == 1 and isinstance(all_data[0], dict) and 'id' in str(all_data[0])) else 'object',
                'collection_complete': True
            },
            'timezone_info': {
                'requested_timezone': timezone_param,
                'response_timezone': response_timezone
            }
        }

        return JsonResponse(response_data, safe=False)

    except requests.exceptions.Timeout:
        logger.error("Navirec API request timed out")
        return JsonResponse({
            'error': 'Navirec API request timed out'
        }, status=408)

    except requests.exceptions.RequestException as e:
        logger.error(f"Navirec API request failed: {str(e)}")
        return JsonResponse({
            'error': f'Navirec API request failed: {str(e)}'
        }, status=500)

    except Exception as e:
        logger.error(f"Unexpected error in Navirec API call: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@require_http_methods(["GET"])
def weather_history_api(request, workorder_id):
    """
    API endpoint to get historical weather data for a workorder's timeframe and location.

    Args:
        request: HTTP request object
        workorder_id: ID of the workorder

    Returns:
        JsonResponse with weather history data or error message
    """
    try:
        weather_service = WeatherHistoryService()
        weather_data = weather_service.get_workorder_weather_history(workorder_id)

        return JsonResponse({
            'success': True,
            'data': weather_data
        })

    except WeatherHistoryServiceError as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=503)

    except Exception as e:
        logger.error(f"Unexpected error in weather history API: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'An unexpected error occurred. Please try again later.'
        }, status=500)


@require_http_methods(["GET"])
def gps_chart_api(request, workorder_id):
    """
    API endpoint to generate GPS speed chart for a specific vehicle and workorder timeframe.

    Query parameters:
    - vehicle_id: ID of the vehicle to get GPS data for

    Returns JSON with the generated GPS chart HTML.
    """
    try:
        vehicle_id = request.GET.get('vehicle_id')
        vehicle_name = request.GET.get('vehicle_name', f'Vehicle {vehicle_id}')
        if not vehicle_id:
            return JsonResponse({
                'error': 'vehicle_id parameter is required'
            }, status=400)

        # Get workorder date range and timezone first (needed for both cached and non-cached paths)
        workorder_start_date, workorder_end_date = get_workorder_date_range(workorder_id)
        set_code, timezone_name = get_set_code_and_timezone(workorder_id)

        if not workorder_start_date or not workorder_end_date:
            return JsonResponse({
                'error': "Could not determine workorder's date range"
            }, status=400)

        # Require the set's local timezone - no fallback to UTC
        if not timezone_name:
            return JsonResponse({
                'error': "Set's local time zone not available. Can't generate the chart without it."
            }, status=400)

        # Convert dates from "fake UTC" (local times) to real UTC for API call
        # Database times are stored as UTC but represent local times (same as weather history)
        local_tz = pytz.timezone(timezone_name)

        # Remove timezone info and treat as local time (same pattern as weather history service)
        start_local = local_tz.localize(workorder_start_date.replace(tzinfo=None))
        end_local = local_tz.localize(workorder_end_date.replace(tzinfo=None))

        # Convert local times to real UTC for API call
        start_utc = start_local.astimezone(pytz.UTC)
        end_utc = end_local.astimezone(pytz.UTC)

        # Format as UTC ISO timestamps (e.g., 2025-07-28T07:00:00Z)
        start_time_iso = start_utc.strftime('%Y-%m-%dT%H:%M:%SZ')
        end_time_iso = end_utc.strftime('%Y-%m-%dT%H:%M:%SZ')

        # Check cache after we have the timezone info
        cache_key = f"gps_chart_data_{workorder_id}_{vehicle_id}"
        cached_data = cache.get(cache_key)
        if cached_data:
            # Re-generate chart HTML with potentially updated vehicle_name using local timeframe for title
            gps_chart_html = generate_gps_speed_chart(
                speed_data=cached_data['speed_data'],
                activity_timeline=cached_data['activity_timeline'],
                vehicle_name=vehicle_name,
                start_time=start_local.isoformat(),  # Use local timeframe for chart title
                end_time=end_local.isoformat(),      # Use local timeframe for chart title
                timezone=timezone_name
            )

            if gps_chart_html is None:
                return JsonResponse({
                    'success': False,
                    'error': "No GPS data available in this workorder's timeframe using this vehicle"
                })

            return JsonResponse({
                'success': True,
                'chart_html': gps_chart_html,
                'vehicle_id': vehicle_id,
                'vehicle_name': vehicle_name,
                'start_time': start_local.isoformat(),  # Return local timeframe for consistency
                'end_time': end_local.isoformat(),      # Return local timeframe for consistency
                'timezone': timezone_name,
                'data_points': len(cached_data['speed_data']),
                'activity_segments': len(cached_data['activity_timeline']),
                'cached': True
            })

        # timezone_to_use = timezone_name  # Already have timezone_name

        # Get Navirec credentials
        account_id, api_key = get_navirec_credentials()

        if not account_id or not api_key:
            return JsonResponse({
                'error': 'Navirec API credentials not configured'
            }, status=500)

        headers = {
            'Accept': 'application/json; version=1.42.0',
            'Authorization': f'Token {api_key}',
            'Host': 'api.navirec.com',
            'User-Agent': 'WorkorderDataApp/1.0.0'
        }

        # Add timezone header to ensure API returns data in the set's local timezone
        if timezone_name and timezone_name.upper() != 'UTC':
            headers['Accept-Timezone'] = timezone_name

        # Call Navirec API for vehicle timeline with retry logic
        api_url = "https://api.navirec.com/vehicle_timeline/"
        params = {
            'vehicle': vehicle_id,
            'start_time': start_time_iso,
            'end_time': end_time_iso
        }

        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(api_url, headers=headers, params=params, timeout=30)

                if response.status_code == 200:
                    break
                elif response.status_code == 429:
                    # Rate limited - wait and retry
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    else:
                        return JsonResponse({
                            'error': 'Navirec API rate limit exceeded. Please try again later.'
                        }, status=429)
                elif response.status_code == 400:
                    # Parse 400 error to provide specific user-friendly messages
                    try:
                        error_details = response.json()
                        error_message = error_details.get('message', '') or error_details.get('error', '') or response.text
                    except:
                        error_message = response.text

                    # Check for common 400 error scenarios and provide clear messages
                    error_lower = error_message.lower()

                    if any(phrase in error_lower for phrase in ['no data', 'no records', 'empty result', 'no timeline', 'no events']):
                        return JsonResponse({
                            'error': f'No GPS data available for vehicle "{vehicle_name}" during this workorder\'s timeframe ({start_local.strftime("%b %d, %Y %H:%M")} - {end_local.strftime("%b %d, %Y %H:%M")})',
                            'vehicle_id': vehicle_id,
                            'start_time': start_local.isoformat(),
                            'end_time': end_local.isoformat()
                        }, status=400)
                    elif any(phrase in error_lower for phrase in ['invalid vehicle', 'vehicle not found', 'vehicle does not exist']):
                        return JsonResponse({
                            'error': f'Vehicle "{vehicle_name}" is not valid or not accessible in your Navirec account',
                            'vehicle_id': vehicle_id
                        }, status=400)
                    elif any(phrase in error_lower for phrase in ['invalid date', 'date format', 'time format']):
                        return JsonResponse({
                            'error': f'Invalid date range: {start_local.strftime("%b %d, %Y %H:%M")} - {end_local.strftime("%b %d, %Y %H:%M")}. Please try a different time period.',
                            'vehicle_id': vehicle_id,
                            'start_time': start_local.isoformat(),
                            'end_time': end_local.isoformat()
                        }, status=400)
                    elif any(phrase in error_lower for phrase in ['date range too large', 'range too big', 'period too long']):
                        return JsonResponse({
                            'error': 'The requested date range is too large. Please try a smaller time period.',
                            'vehicle_id': vehicle_id,
                            'start_time': start_local.isoformat(),
                            'end_time': end_local.isoformat()
                        }, status=400)
                    else:
                        # Generic 400 error with context
                        return JsonResponse({
                            'error': f'Navirec 400 Bad Request error.',
                            'vehicle_id': vehicle_id,
                            'start_time': start_local.isoformat(),
                            'end_time': end_local.isoformat()
                        }, status=400)
                elif response.status_code == 404:
                    return JsonResponse({
                        'error': f'Vehicle "{vehicle_name}" was not found in your Navirec account',
                        'vehicle_id': vehicle_id
                    }, status=404)
                else:
                    return JsonResponse({
                        'error': f'Failed to fetch GPS data from Navirec API (Error {response.status_code}). Please try again later.',
                        'vehicle_id': vehicle_id
                    }, status=500)

            except requests.exceptions.Timeout:
                if attempt == max_retries - 1:
                    return JsonResponse({
                        'error': 'Request timeout while fetching GPS data'
                    }, status=500)
                time.sleep(1)
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    return JsonResponse({
                        'error': f'Failed to fetch GPS data: {str(e)}'
                    }, status=500)
                time.sleep(1)

        if response.status_code == 200:
            api_data = response.json()
            speed_data, activity_timeline, api_start_time, api_end_time = process_navirec_timeline_data(api_data)

            if speed_data or activity_timeline:

                # Cache the API data for future use with local timeframe for titles
                cache_data = {
                    'speed_data': speed_data,
                    'activity_timeline': activity_timeline,
                    'start_time': start_local.isoformat(),  # Store local timeframe for consistent titles
                    'end_time': end_local.isoformat(),      # Store local timeframe for consistent titles
                    'timezone': timezone_name
                }
                cache_ttl = 3600  # 1 hour (same as weather history)
                cache.set(cache_key, cache_data, cache_ttl)

                # Generate the GPS chart using local timeframe for title
                gps_chart_html = generate_gps_speed_chart(
                    speed_data=speed_data,
                    activity_timeline=activity_timeline,
                    vehicle_name=vehicle_name,
                    start_time=start_local.isoformat(),  # Use local timeframe for chart title
                    end_time=end_local.isoformat(),      # Use local timeframe for chart title
                    timezone=timezone_name
                )

                if gps_chart_html is None:
                    return JsonResponse({
                        'success': False,
                        'error': "No GPS data available in this workorder's timeframe using this vehicle"
                    })

                return JsonResponse({
                    'success': True,
                    'chart_html': gps_chart_html,
                    'vehicle_id': vehicle_id,
                    'vehicle_name': vehicle_name,
                    'start_time': start_local.isoformat(),  # Return local timeframe
                    'end_time': end_local.isoformat(),      # Return local timeframe
                    'timezone': timezone_name,
                    'data_points': len(speed_data),
                    'activity_segments': len(activity_timeline),
                    'cached': False
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': "No GPS data available in this workorder's timeframe using this vehicle",
                    'vehicle_id': vehicle_id,
                    'start_time': start_local.isoformat(),
                    'end_time': end_local.isoformat()
                })
        else:
            return JsonResponse({
                'error': f'Failed to fetch GPS data from Navirec API (Error {response.status_code}). Please try again later.',
                'vehicle_id': vehicle_id
            }, status=500)

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to generate GPS chart: {str(e)}'
        }, status=500)