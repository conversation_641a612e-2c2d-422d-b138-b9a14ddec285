/**
 * GPS Tab Component and Navirec API Integration
 */



/**
 * Call any Navirec API endpoint through Django backend proxy
 * @param {string} endpoint - API endpoint (e.g., 'vehicles', 'vehicle_timeline')
 * @param {Object} params - Optional query parameters
 * @param {string} timezone - Optional timezone (defaults to UTC)
 */
async function callNavirecAPI(endpoint = '', params = {}, timezone = 'UTC') {
    try {
        const csrfToken = document.cookie
            .split('; ')
            .find(row => row.startsWith('csrftoken='))
            ?.split('=')[1] || '';

        const url = new URL('/workorders/navirec-api/', window.location.origin);
        url.searchParams.set('endpoint', endpoint);

        if (timezone && timezone !== 'UTC') {
            url.searchParams.set('timezone', timezone);
        }

        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                url.searchParams.set(key, value);
            }
        });

        console.log(`🚀 Calling Navirec API: ${endpoint}`, { params, timezone });

        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        });

        if (response.ok) {
            const data = await response.json();
            console.log(`✅ Navirec API response for ${endpoint}:`, data);
            return data.error ? null : data;
        } else {
            console.error(`❌ Navirec API error for ${endpoint}:`, response.status, response.statusText);
            return null;
        }
    } catch (error) {
        console.error(`❌ Navirec API exception for ${endpoint}:`, error);
        return null;
    }
}

/**
 * GPS Tab Component - Alpine.js component for GPS tab functionality
 */
function gpsTabComponent() {
    return {
        vehicles: [],
        vehiclesLoaded: false,
        loadingVehicles: false,
        vehicleError: null,
        selectedVehicle: '',
        tomSelectInstance: null,
        gpsChartLoading: false,
        gpsChartError: null,
        currentChartHtml: null,
        dataCache: new Map(),
        chartCache: new Map(),

        init() {
            // Component initialization
        },

        async loadVehicles() {
            if (this.loadingVehicles || this.vehiclesLoaded) return;

            this.loadingVehicles = true;
            this.vehicleError = null;

            try {
                const response = await callNavirecAPI('vehicles');

                if (response && response.data) {
                    this.vehicles = response.data.filter(vehicle =>
                        vehicle && vehicle.id && vehicle.name && vehicle.name.trim() !== ''
                    );
                    this.vehiclesLoaded = true;

                    this.$nextTick(() => {
                        this.initTomSelect();
                    });
                } else {
                    this.vehicleError = 'Failed to load vehicles';
                }
            } catch (error) {
                this.vehicleError = 'Error loading vehicles';
            } finally {
                this.loadingVehicles = false;
            }
        },

        initTomSelect() {
            if (this.tomSelectInstance) {
                this.tomSelectInstance.destroy();
            }

            const selectElement = this.$refs.vehicleSelect;
            if (!selectElement) return;

            this.tomSelectInstance = new TomSelect(selectElement, {
                placeholder: `Select vehicle (${this.vehicles.length})`,
                allowEmptyOption: false,
                maxItems: 1,
                hideSelected: false,
                closeAfterSelect: true,
                animate: false,
                searchField: 'text',
                sortField: 'text',
                selectOnTab: false,
                create: false,
                maxOptions: null,
                preload: true,
                onChange: (value) => {
                    this.selectedVehicle = value;
                    if (value) {
                        this.loadGpsChart(value);
                    } else {
                        this.currentChartHtml = null;
                        this.gpsChartError = null;
                        const chartContainer = document.getElementById('gps-chart-container');
                        if (chartContainer) {
                            chartContainer.innerHTML = '';
                        }
                    }
                }
            });

            this.tomSelectInstance.clear(true);
        },

        async loadGpsChart(vehicleId) {
            if (!vehicleId) return;

            // Check cache first
            if (this.dataCache.has(vehicleId) || this.gpsChartLoading) {
                if (this.dataCache.has(vehicleId)) {
                    this.gpsChartError = null;
                    this.currentChartHtml = this.chartCache.get(vehicleId);

                    const chartContainer = document.getElementById('gps-chart-container');
                    if (chartContainer && chartContainer.innerHTML.trim() === this.currentChartHtml.trim()) {
                        return;
                    }
                    this.renderChart();
                }
                return;
            }

            // Clear chart for new data
            this.currentChartHtml = null;
            this.gpsChartError = null;
            const chartContainer = document.getElementById('gps-chart-container');
            if (chartContainer) {
                chartContainer.innerHTML = '';
            }

            this.gpsChartLoading = true;

            try {
                const workorderId = this.getWorkorderId();
                if (!workorderId) {
                    throw new Error('Could not determine workorder ID');
                }

                const csrfToken = document.cookie
                    .split('; ')
                    .find(row => row.startsWith('csrftoken='))
                    ?.split('=')[1] || '';

                const selectedVehicleObj = this.vehicles.find(v => v.id === vehicleId);
                const vehicleName = selectedVehicleObj ? selectedVehicleObj.name : vehicleId;

                const url = `/workorders/gps-chart/${workorderId}/?vehicle_id=${vehicleId}&vehicle_name=${encodeURIComponent(vehicleName)}`;

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': csrfToken,
                        'Content-Type': 'application/json',
                    },
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.success && data.chart_html) {
                        this.currentChartHtml = data.chart_html;

                        // Cache data and chart
                        this.dataCache.set(vehicleId, {
                            vehicle_id: data.vehicle_id,
                            vehicle_name: data.vehicle_name,
                            start_time: data.start_time,
                            end_time: data.end_time,
                            timezone: data.timezone,
                            data_points: data.data_points,
                            activity_segments: data.activity_segments,
                            cached: data.cached || false
                        });
                        this.chartCache.set(vehicleId, data.chart_html);

                        this.renderChart();
                    } else {
                        this.gpsChartError = data.error || 'No GPS data available for this vehicle and timeframe';
                        this.currentChartHtml = null;
                        const chartContainer = document.getElementById('gps-chart-container');
                        if (chartContainer) {
                            chartContainer.innerHTML = '';
                        }
                    }
                } else {
                    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                    this.gpsChartError = errorData.error || `Request failed with status ${response.status}`;
                    this.currentChartHtml = null;
                    const chartContainer = document.getElementById('gps-chart-container');
                    if (chartContainer) {
                        chartContainer.innerHTML = '';
                    }
                }

            } catch (error) {
                this.gpsChartError = 'Failed to load GPS chart. Please try again.';
                this.currentChartHtml = null;
                const chartContainer = document.getElementById('gps-chart-container');
                if (chartContainer) {
                    chartContainer.innerHTML = '';
                }
            } finally {
                this.gpsChartLoading = false;
            }
        },

        renderChart() {
            this.$nextTick(() => {
                const chartContainer = document.getElementById('gps-chart-container');
                if (chartContainer && this.currentChartHtml) {
                    // Destroy existing Plotly chart if it exists
                    const existingChart = chartContainer.querySelector('#gps-speed-chart');
                    if (existingChart && window.Plotly) {
                        window.Plotly.purge(existingChart);
                    }

                    chartContainer.style.visibility = 'hidden';
                    chartContainer.innerHTML = this.currentChartHtml;

                    // Execute script tags without adding to DOM
                    const scripts = chartContainer.querySelectorAll('script');
                    scripts.forEach(script => {
                        if (!script.src && script.textContent) {
                            // Execute inline scripts directly
                            try {
                                new Function(script.textContent)();
                            } catch (e) {
                                console.error('Error executing chart script:', e);
                            }
                        }
                    });

                    // Resize and show chart
                    setTimeout(() => {
                        if (window.Plotly) {
                            const plotlyDiv = chartContainer.querySelector('#gps-speed-chart');
                            if (plotlyDiv) {
                                window.Plotly.Plots.resize(plotlyDiv.id);
                            }
                        }
                        chartContainer.style.visibility = 'visible';
                    }, 150);
                }
            });
        },

        clearCache() {
            this.dataCache.clear();
            this.chartCache.clear();
        },

        getWorkorderId() {
            const pathMatch = window.location.pathname.match(/\/workorder\/(\d+)\//);
            if (pathMatch) {
                return pathMatch[1];
            }

            const urlParams = new URLSearchParams(window.location.search);
            const workorderId = urlParams.get('workorder_id');
            if (workorderId) {
                return workorderId;
            }

            const workorderElement = document.querySelector('[data-workorder-id]');
            if (workorderElement) {
                return workorderElement.getAttribute('data-workorder-id');
            }

            return null;
        },

        destroy() {
            if (this.tomSelectInstance) {
                this.tomSelectInstance.destroy();
                this.tomSelectInstance = null;
            }
            // Clear caches to prevent memory leaks
            this.dataCache.clear();
            this.chartCache.clear();
        }
    };
}

// Make functions globally available for manual testing
window.callNavirecAPI = callNavirecAPI;
window.gpsTabComponent = gpsTabComponent;
